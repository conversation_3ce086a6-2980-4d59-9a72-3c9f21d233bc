---
description:
globs:
alwaysApply: false
---
# App Structure & Routing

## App Directory Structure
The project uses Next.js App Router with the following main routes:

### Core Application Files
- [app/layout.tsx](mdc:app/layout.tsx) - Root layout with theme provider, header, and bottom navigation
- [app/page.tsx](mdc:app/page.tsx) - Homepage (redirects to landing)
- [app/globals.css](mdc:app/globals.css) - Global styles and CSS variables

### Main Routes
- [app/(landing)/](mdc:app/(landing)/) - Landing page (route group)
- [app/dashboard/](mdc:app/dashboard/) - User dashboard
- [app/create-shipment/](mdc:app/create-shipment/) - Create new shipment
- [app/find-courier/](mdc:app/find-courier/) - Find available couriers
- [app/track-delivery/](mdc:app/track-delivery/) - Track delivery status
- [app/messages/](mdc:app/messages/) - Messaging system
- [app/profile/](mdc:app/profile/) - User profile management
  - [app/profile/notifications/](mdc:app/profile/notifications/) - Notification settings
  - [app/profile/verification/](mdc:app/profile/verification/) - Account verification
- [app/courier/](mdc:app/courier/) - Courier-specific features
  - [app/courier/post-route/](mdc:app/courier/post-route/) - Post delivery routes

## Layout Structure
- **Header**: [components/sutway/app-header.tsx](mdc:components/sutway/app-header.tsx)
- **Bottom Navigation**: [components/sutway/bottom-nav.tsx](mdc:components/sutway/bottom-nav.tsx)
- **Theme Provider**: [components/theme-provider.tsx](mdc:components/theme-provider.tsx)

## Route Groups
- `(landing)` - Route group for landing page (doesn't affect URL structure)
