---
description:
globs:
alwaysApply: false
---
# Development Conventions

## Code Style & Standards

### TypeScript
- Use TypeScript for all new files
- Define proper types and interfaces
- Avoid `any` type - use proper typing
- Use React.FC or explicit return types for components

### Import/Export Patterns
```typescript
// Preferred import order:
1. React imports
2. Next.js imports
3. Third-party libraries
4. UI components (@/components/ui/*)
5. Custom components (@/components/sutway/*)
6. Utilities (@/lib/*)
7. Types and interfaces
```

### Component Conventions
- Use PascalCase for component names
- Place components in appropriate directories:
  - `components/ui/` - Reusable UI primitives
  - `components/sutway/` - Application-specific components
  - `app/*/` - Page components (page.tsx, layout.tsx)

### Styling Guidelines
- Use Tailwind CSS classes
- Leverage the `cn()` utility from [lib/utils.ts](mdc:lib/utils.ts) for conditional classes
- Follow the design system defined in [tailwind.config.ts](mdc:tailwind.config.ts)
- Use CSS variables for theming (defined in [app/globals.css](mdc:app/globals.css))

## File Naming
- **Pages**: `page.tsx`, `layout.tsx`, `loading.tsx`, `error.tsx`
- **Components**: `kebab-case.tsx` (e.g., `app-header.tsx`)
- **Utilities**: `kebab-case.ts` (e.g., `utils.ts`)
- **Types**: Use descriptive names with `.types.ts` suffix if separate file

## Database Integration
- Use Supabase as the primary database
- Keep database-related logic in appropriate service files
- Use proper error handling for database operations

## Performance Considerations
- Use Next.js built-in optimizations (Image, Link components)
- Implement proper loading states
- Consider Server Components vs Client Components
- Use dynamic imports for large components when appropriate

## Mobile-First Approach
- Design for mobile first (note the [bottom-nav.tsx](mdc:components/sutway/bottom-nav.tsx))
- Use responsive design patterns
- Test on various screen sizes

## Package Management
- Use `pnpm` for package management
- Keep dependencies up to date
- Add new dependencies thoughtfully - check if functionality exists in current stack first
