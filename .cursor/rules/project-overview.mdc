---
description:
globs:
alwaysApply: false
---
# Sutway App - Project Overview

## About
Sutway is a peer-to-peer delivery platform for the Ethiopian-American community. It's built with Next.js 15, React 19, TypeScript, and Tailwind CSS.

## Technology Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives with custom components
- **Package Manager**: pnpm
- **Database**: Supabase (as indicated in project memories)
- **Theme**: Dark/light mode support via next-themes

## Key Configuration Files
- [package.json](mdc:package.json) - Dependencies and scripts
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS configuration
- [next.config.mjs](mdc:next.config.mjs) - Next.js configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [components.json](mdc:components.json) - UI component configuration

## Development Commands
- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
