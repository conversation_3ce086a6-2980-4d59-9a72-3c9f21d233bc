---
description:
globs:
alwaysApply: false
---
# Components Guide

## Component Organization

### UI Components (`components/ui/`)
Reusable UI primitives based on Radix UI and configured through [components.json](mdc:components.json). These are the building blocks for the design system.

### Sutway Components (`components/sutway/`)
Application-specific components for the Sutway platform:

- [components/sutway/app-header.tsx](mdc:components/sutway/app-header.tsx) - Main application header
- [components/sutway/bottom-nav.tsx](mdc:components/sutway/bottom-nav.tsx) - Mobile bottom navigation
- [components/sutway/courier-card.tsx](mdc:components/sutway/courier-card.tsx) - Courier display card
- [components/sutway/trust-badge.tsx](mdc:components/sutway/trust-badge.tsx) - Trust/verification badge
- [components/sutway/landing/](mdc:components/sutway/landing/) - Landing page specific components

### Theme Components
- [components/theme-provider.tsx](mdc:components/theme-provider.tsx) - Theme context provider for dark/light mode

## Component Patterns

### Import Conventions
- Use `@/components/ui/` for UI primitives
- Use `@/components/sutway/` for application components
- Use `@/lib/utils` for utility functions ([lib/utils.ts](mdc:lib/utils.ts))

### Styling Approach
- Tailwind CSS for styling
- Class variance authority (CVA) for component variants
- Tailwind merge (twMerge) for conditional classes
- Custom CSS variables defined in [app/globals.css](mdc:app/globals.css)

### Component Structure
Components typically follow this pattern:
1. React imports
2. UI component imports
3. Type definitions
4. Component implementation
5. Export statement

## Utilities
- [lib/utils.ts](mdc:lib/utils.ts) - Utility functions including `cn()` for className merging
