// This is the layout specifically for your landing page sections.
// It MUST be present in the app/(landing)/ folder.
import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "../globals.css" // Path relative to app/(landing)/layout.tsx
import { ThemeProvider } from "@/components/theme-provider"
import LandingHeader from "@/components/sutway/landing/landing-header"
import LandingFooter from "@/components/sutway/landing/landing-footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Sutway - Community Delivery You Can Trust",
  description:
    "Sutway connects the Ethiopian-American community for reliable, affordable peer-to-peer shipping of cultural items and more.",
}

export default function LandingLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <div className="flex flex-col min-h-screen bg-background">
            <LandingHeader />
            <main className="flex-grow">{children}</main>
            <LandingFooter />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
