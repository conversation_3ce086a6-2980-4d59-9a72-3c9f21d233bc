"use client"

import CourierCard from "@/components/sutway/courier-card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search } from "lucide-react"
import { useState } from "react"

const cities = [
  "Any",
  "Washington, D.C.",
  "Minneapolis, MN",
  "Seattle, WA",
  "Atlanta, GA",
  "Los Angeles, CA",
  "Dallas, TX",
]

// Expanded sample couriers for this page
const allCouriers = [
  {
    id: "c1",
    name: "Abebe Bikila",
    rating: 4.8,
    culturalRespect: 5,
    deliveries: 120,
    memberSince: "Jan 2023",
    verifiedBadges: ["ID Verified", "Church Member", "Community Endorsement"],
    route: "DC to LA",
    eta: "3 days",
    profilePic: "/placeholder-user.jpg",
  },
  {
    id: "c2",
    name: "<PERSON><PERSON>",
    rating: 4.5,
    culturalRespect: 4,
    deliveries: 75,
    memberSince: "Mar 2023",
    verifiedBadges: ["ID Verified"],
    route: "Minneapolis to Atlanta",
    profilePic: "/placeholder-user.jpg",
  },
  {
    id: "c3",
    name: "<PERSON>",
    rating: 4.9,
    culturalRespect: 5,
    deliveries: 200,
    memberSince: "Dec 2022",
    verifiedBadges: ["ID Verified", "Professional Association"],
    route: "Seattle to Dallas",
    profilePic: "/placeholder-user.jpg",
  },
  {
    id: "c4",
    name: "Liya M.",
    rating: 4.2,
    culturalRespect: 3,
    deliveries: 30,
    memberSince: "Feb 2024",
    verifiedBadges: [],
    route: "Atlanta to DC",
    profilePic: "/placeholder-user.jpg",
  },
]

export default function FindCourierPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [originFilter, setOriginFilter] = useState("Any")
  const [destinationFilter, setDestinationFilter] = useState("Any")

  const filteredCouriers = allCouriers.filter((courier) => {
    const nameMatch = courier.name.toLowerCase().includes(searchTerm.toLowerCase())
    const originMatch =
      originFilter === "Any" ||
      (courier.route && courier.route.toLowerCase().includes(originFilter.toLowerCase().split(",")[0]))
    const destinationMatch =
      destinationFilter === "Any" ||
      (courier.route && courier.route.toLowerCase().includes(destinationFilter.toLowerCase().split(",")[0]))
    return nameMatch && originMatch && destinationMatch
  })

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Find a Courier</h2>
      <div className="space-y-4 p-4 border rounded-lg bg-card">
        <div className="relative">
          <Search className="absolute left-2.5 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search by courier name..."
            className="pl-8 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Select value={originFilter} onValueChange={setOriginFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by origin" />
            </SelectTrigger>
            <SelectContent>
              {cities.map((city) => (
                <SelectItem key={`orig-${city}`} value={city}>
                  {city === "Any" ? "Any Origin" : city}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={destinationFilter} onValueChange={setDestinationFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by destination" />
            </SelectTrigger>
            <SelectContent>
              {cities.map((city) => (
                <SelectItem key={`dest-${city}`} value={city}>
                  {city === "Any" ? "Any Destination" : city}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {filteredCouriers.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredCouriers.map((courier) => (
            <CourierCard key={courier.id} courier={courier} showSelectButton={false} showContactButton={true} />
          ))}
        </div>
      ) : (
        <p className="text-center text-muted-foreground py-8">
          No couriers match your criteria. Try adjusting your filters.
        </p>
      )}
    </div>
  )
}
