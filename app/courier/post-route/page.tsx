"use client"

import type React from "react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, CheckCircle, Truck } from "lucide-react"
import { useState } from "react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

const cities = ["Washington, D.C.", "Minneapolis, MN", "Seattle, WA", "Atlanta, GA", "Los Angeles, CA", "Dallas, TX"]
const itemPreferences = ["Injera", "Coffee", "Spices", "<PERSON>lothes", "Religious Items", "General Goods"]

export default function PostRoutePage() {
  const [formData, setFormData] = useState({
    originCity: "",
    destinationCity: "",
    departureDate: undefined as Date | undefined,
    cargoCapacity: "", // e.g., "Small box", "Up to 2 suitcases"
    itemPreferences: [] as string[],
    notes: "",
  })
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleDateSelect = (date: Date | undefined) => {
    setFormData((prev) => ({ ...prev, departureDate: date }))
  }

  const handleItemPreferenceToggle = (item: string) => {
    setFormData((prev) => {
      const newPrefs = prev.itemPreferences.includes(item)
        ? prev.itemPreferences.filter((p) => p !== item)
        : [...prev.itemPreferences, item]
      return { ...prev, itemPreferences: newPrefs }
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // API call to submit route
    console.log("Route Data:", formData)
    setIsSubmitted(true)
    // Reset form or redirect after a delay
    setTimeout(() => {
      setIsSubmitted(false)
      // setFormData({ originCity: "", destinationCity: "", departureDate: undefined, cargoCapacity: "", itemPreferences: [], notes: "" });
    }, 3000)
  }

  if (isSubmitted) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-4">
        <CheckCircle className="h-16 w-16 text-green-500" />
        <h2 className="text-2xl font-semibold">Travel Route Posted!</h2>
        <p className="text-muted-foreground">
          Your route from {formData.originCity} to {formData.destinationCity} has been successfully posted.
          <br />
          Senders can now find your availability.
        </p>
        <Button onClick={() => setIsSubmitted(false)} variant="outline">
          Post Another Route
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Truck className="h-8 w-8 text-orange-600" />
        <h2 className="text-2xl font-semibold">Post Your Travel Route</h2>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Enter Your Travel Details</CardTitle>
          <CardDescription>Let senders know when and where you're traveling.</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="originCity">Origin City</Label>
                <Select
                  required
                  onValueChange={(value) => handleInputChange("originCity", value)}
                  value={formData.originCity}
                >
                  <SelectTrigger id="originCity">
                    <SelectValue placeholder="Select origin city" />
                  </SelectTrigger>
                  <SelectContent>
                    {cities.map((city) => (
                      <SelectItem key={`orig-${city}`} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="destinationCity">Destination City</Label>
                <Select
                  required
                  onValueChange={(value) => handleInputChange("destinationCity", value)}
                  value={formData.destinationCity}
                >
                  <SelectTrigger id="destinationCity">
                    <SelectValue placeholder="Select destination city" />
                  </SelectTrigger>
                  <SelectContent>
                    {cities.map((city) => (
                      <SelectItem key={`dest-${city}`} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="departureDate">Departure Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !formData.departureDate && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.departureDate ? format(formData.departureDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.departureDate}
                    onSelect={handleDateSelect}
                    initialFocus
                    disabled={(date) => date < new Date(new Date().setDate(new Date().getDate() - 1))} // Disable past dates
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label htmlFor="cargoCapacity">Cargo Capacity</Label>
              <Input
                id="cargoCapacity"
                placeholder="e.g., 1 small bag, 2 large suitcases"
                value={formData.cargoCapacity}
                onChange={(e) => handleInputChange("cargoCapacity", e.target.value)}
              />
            </div>
            <div>
              <Label>Item Preferences (what you're willing to carry)</Label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-2">
                {itemPreferences.map((item) => (
                  <Button
                    key={item}
                    type="button"
                    variant={formData.itemPreferences.includes(item) ? "default" : "outline"}
                    onClick={() => handleItemPreferenceToggle(item)}
                    className={cn(formData.itemPreferences.includes(item) && "bg-orange-500 hover:bg-orange-600")}
                  >
                    {item}
                  </Button>
                ))}
              </div>
            </div>
            <div>
              <Label htmlFor="notes">Additional Notes (optional)</Label>
              <Textarea
                id="notes"
                placeholder="Any specific instructions or details for senders."
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700">
              Post Travel Route
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
