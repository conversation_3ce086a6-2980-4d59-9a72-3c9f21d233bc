"use client"

import Link from "next/link"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import {
  ChevronLeft,
  ChevronRight,
  MapPin,
  Home,
  Building,
  CreditCard,
  CircleDollarSign,
  CheckCircle2,
} from "lucide-react"
import CourierCard from "@/components/sutway/courier-card" // We'll create this next

const steps = [
  { id: "itemDetails", name: "Item Details" },
  { id: "routeSelection", name: "Route & Pickup" },
  { id: "courierSelection", name: "Select Courier" },
  { id: "payment", name: "Payment" },
  { id: "confirmation", name: "Confirmation" },
]

const itemCategories = ["Injera", "Coffee", "Spices", "Clothes", "Religious Items", "Other"]
const cities = ["Washington, D.C.", "Minneapolis, MN", "Seattle, WA", "Atlanta, GA", "Los Angeles, CA", "Dallas, TX"] // Added Dallas from project description
const pickupLocationTypes = [
  { value: "church", label: "Ethiopian Church", icon: <Home className="mr-2 h-4 w-4" /> },
  { value: "restaurant_market", label: "Ethiopian Restaurant/Market", icon: <Building className="mr-2 h-4 w-4" /> },
  { value: "home", label: "Home Address", icon: <Home className="mr-2 h-4 w-4" /> },
  { value: "other", label: "Other", icon: <MapPin className="mr-2 h-4 w-4" /> },
]

// Sample couriers
const sampleCouriers = [
  {
    id: "c1",
    name: "Abebe Bikila",
    rating: 4.8,
    culturalRespect: 5,
    deliveries: 120,
    memberSince: "Jan 2023",
    verifiedBadges: ["ID Verified", "Church Member"],
    route: "DC to LA",
    eta: "3 days",
    price: 35,
    profilePic: "/placeholder-user.jpg",
  },
  {
    id: "c2",
    name: "Hana G.",
    rating: 4.5,
    culturalRespect: 4,
    deliveries: 75,
    memberSince: "Mar 2023",
    verifiedBadges: ["ID Verified"],
    route: "Minneapolis to Atlanta",
    eta: "2 days",
    price: 28,
    profilePic: "/placeholder-user.jpg",
  },
]

export default function CreateShipmentPage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState({
    itemCategory: "",
    otherItemName: "",
    itemDescription: "",
    itemPhoto: null as File | null,
    originCity: "",
    destinationCity: "",
    pickupLocationType: "",
    pickupAddress: "",
    deliveryAddress: "",
    selectedCourierId: "",
    paymentMethod: "",
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      handleInputChange("itemPhoto", event.target.files[0])
    }
  }

  const nextStep = () => setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1))
  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 0))

  const progressValue = ((currentStep + 1) / steps.length) * 100

  const handleSubmit = () => {
    // Handle final submission
    console.log("Shipment Data:", formData)
    alert("Shipment request submitted! (See console for data)")
    setCurrentStep(steps.findIndex((step) => step.id === "confirmation"))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Create New Shipment</h2>
        <span className="text-sm text-muted-foreground">
          {steps[currentStep].name} ({currentStep + 1}/{steps.length})
        </span>
      </div>
      <Progress value={progressValue} className="w-full" />

      <Card>
        <CardContent className="pt-6">
          {steps[currentStep].id === "itemDetails" && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="itemCategory">Item Category</Label>
                <Select
                  onValueChange={(value) => handleInputChange("itemCategory", value)}
                  value={formData.itemCategory}
                >
                  <SelectTrigger id="itemCategory">
                    <SelectValue placeholder="Select item type" />
                  </SelectTrigger>
                  <SelectContent>
                    {itemCategories.map((cat) => (
                      <SelectItem key={cat} value={cat.toLowerCase().replace(/\s+/g, "_")}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formData.itemCategory === "other" && (
                  <Input
                    id="otherItemName"
                    placeholder="Specify item name"
                    className="mt-2"
                    value={formData.otherItemName}
                    onChange={(e) => handleInputChange("otherItemName", e.target.value)}
                  />
                )}
              </div>
              <div>
                <Label htmlFor="itemDescription">Item Description & Handling (e.g. fragile, needs refrigeration)</Label>
                <Textarea
                  id="itemDescription"
                  placeholder="Describe the item and any special handling instructions."
                  value={formData.itemDescription}
                  onChange={(e) => handleInputChange("itemDescription", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="itemPhoto">Item Photo (Optional, recommended for food/religious items)</Label>
                <Input id="itemPhoto" type="file" onChange={handleFileChange} accept="image/*" />
                {formData.itemPhoto && (
                  <p className="text-sm text-muted-foreground mt-1">Selected: {formData.itemPhoto.name}</p>
                )}
              </div>
            </div>
          )}

          {steps[currentStep].id === "routeSelection" && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="originCity">Origin City</Label>
                <Select onValueChange={(value) => handleInputChange("originCity", value)} value={formData.originCity}>
                  <SelectTrigger id="originCity">
                    <SelectValue placeholder="Select origin city" />
                  </SelectTrigger>
                  <SelectContent>
                    {cities.map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="destinationCity">Destination City</Label>
                <Select
                  onValueChange={(value) => handleInputChange("destinationCity", value)}
                  value={formData.destinationCity}
                >
                  <SelectTrigger id="destinationCity">
                    <SelectValue placeholder="Select destination city" />
                  </SelectTrigger>
                  <SelectContent>
                    {cities.map((city) => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Pickup Location Type</Label>
                <RadioGroup
                  value={formData.pickupLocationType}
                  onValueChange={(value) => handleInputChange("pickupLocationType", value)}
                  className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2"
                >
                  {pickupLocationTypes.map((type) => (
                    <Label
                      key={type.value}
                      htmlFor={type.value}
                      className="flex items-center space-x-2 border p-3 rounded-md hover:bg-accent cursor-pointer [&:has([data-state=checked])]:border-orange-500"
                    >
                      <RadioGroupItem value={type.value} id={type.value} />
                      {type.icon}
                      <span>{type.label}</span>
                    </Label>
                  ))}
                </RadioGroup>
              </div>
              <div>
                <Label htmlFor="pickupAddress">Pickup Address / Details</Label>
                <Textarea
                  id="pickupAddress"
                  placeholder="Full address or specific instructions for pickup."
                  value={formData.pickupAddress}
                  onChange={(e) => handleInputChange("pickupAddress", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="deliveryAddress">Delivery Address (Destination)</Label>
                <Textarea
                  id="deliveryAddress"
                  placeholder="Full address for delivery at the destination."
                  value={formData.deliveryAddress}
                  onChange={(e) => handleInputChange("deliveryAddress", e.target.value)}
                />
              </div>
            </div>
          )}

          {steps[currentStep].id === "courierSelection" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Available Couriers for Your Route</h3>
              <p className="text-sm text-muted-foreground">
                Showing couriers traveling from {formData.originCity || "your origin"} to{" "}
                {formData.destinationCity || "your destination"}.
              </p>
              {sampleCouriers.filter(
                (c) =>
                  c.route.includes(formData.originCity.split(",")[0]) &&
                  c.route.includes(formData.destinationCity.split(",")[0]),
              ).length > 0 ? (
                sampleCouriers
                  .filter(
                    (c) =>
                      c.route.includes(formData.originCity.split(",")[0]) &&
                      c.route.includes(formData.destinationCity.split(",")[0]),
                  ) // Basic filter
                  .map((courier) => (
                    <CourierCard
                      key={courier.id}
                      courier={courier}
                      isSelected={formData.selectedCourierId === courier.id}
                      onSelect={() => handleInputChange("selectedCourierId", courier.id)}
                    />
                  ))
              ) : (
                <p className="text-center text-muted-foreground py-4">
                  No couriers currently match your exact route. Try broadening your search or check back later.
                </p>
              )}
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md text-sm text-green-700">
                <p>
                  Sutway is typically <span className="font-bold">25-30% cheaper</span> than traditional shipping
                  methods like FedEx or UPS for similar routes!
                </p>
              </div>
            </div>
          )}

          {steps[currentStep].id === "payment" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Payment Method</h3>
              <RadioGroup
                value={formData.paymentMethod}
                onValueChange={(value) => handleInputChange("paymentMethod", value)}
                className="space-y-2"
              >
                <Label
                  htmlFor="cash_pickup"
                  className="flex items-center space-x-2 border p-3 rounded-md hover:bg-accent cursor-pointer [&:has([data-state=checked])]:border-orange-500"
                >
                  <RadioGroupItem value="cash_pickup" id="cash_pickup" />
                  <CircleDollarSign className="mr-2 h-5 w-5 text-green-600" />
                  <span>Cash at Pickup</span>
                </Label>
                <Label
                  htmlFor="cash_delivery"
                  className="flex items-center space-x-2 border p-3 rounded-md hover:bg-accent cursor-pointer [&:has([data-state=checked])]:border-orange-500"
                >
                  <RadioGroupItem value="cash_delivery" id="cash_delivery" />
                  <CircleDollarSign className="mr-2 h-5 w-5 text-green-600" />
                  <span>Cash at Delivery</span>
                </Label>
                <Label
                  htmlFor="digital"
                  className="flex items-center space-x-2 border p-3 rounded-md hover:bg-accent cursor-pointer [&:has([data-state=checked])]:border-orange-500"
                >
                  <RadioGroupItem value="digital" id="digital" />
                  <CreditCard className="mr-2 h-5 w-5 text-blue-600" />
                  <span>Digital Payment (Card, PayPal, etc.)</span>
                </Label>
                {/* Add Split Payment if needed */}
              </RadioGroup>
              {formData.paymentMethod === "digital" && (
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle>Enter Card Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <Label htmlFor="cardNumber">Card Number</Label>
                      <Input id="cardNumber" placeholder="•••• •••• •••• ••••" />
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label htmlFor="expiryDate">Expiry Date</Label>
                        <Input id="expiryDate" placeholder="MM/YY" />
                      </div>
                      <div>
                        <Label htmlFor="cvc">CVC</Label>
                        <Input id="cvc" placeholder="•••" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {steps[currentStep].id === "confirmation" && (
            <div className="text-center space-y-4 py-8">
              <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto" />
              <h3 className="text-2xl font-semibold">Shipment Request Sent!</h3>
              <p className="text-muted-foreground">
                Your request for sending{" "}
                {formData.itemCategory === "other" ? formData.otherItemName : formData.itemCategory} from{" "}
                {formData.originCity} to {formData.destinationCity} has been submitted.
              </p>
              <p className="text-muted-foreground">
                The selected courier ({sampleCouriers.find((c) => c.id === formData.selectedCourierId)?.name || "N/A"})
                will be notified. You can track progress in your dashboard.
              </p>
              <Button asChild className="bg-orange-600 hover:bg-orange-700">
                <Link href="/dashboard">Back to Dashboard</Link>
              </Button>
            </div>
          )}
        </CardContent>

        {steps[currentStep].id !== "confirmation" && (
          <CardFooter className="flex justify-between pt-6 border-t">
            <Button variant="outline" onClick={prevStep} disabled={currentStep === 0}>
              <ChevronLeft className="mr-2 h-4 w-4" /> Previous
            </Button>
            {currentStep === steps.length - 2 ? ( // Payment step, next is confirmation
              <Button onClick={handleSubmit} className="bg-green-600 hover:bg-green-700">
                Confirm & Pay <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={nextStep}
                disabled={currentStep === steps.length - 1}
                className="bg-orange-600 hover:bg-orange-700"
              >
                Next <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
