"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Package, Truck, Home, Clock, MessageSquare } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

// Sample tracking data
const sampleShipment = {
  id: "SUT12345",
  item: "Traditional Clothing",
  origin: "Washington, D.C.",
  destination: "Seattle, WA",
  courier: { name: "<PERSON><PERSON>", profilePic: "/placeholder-user.jpg" },
  status: "In Transit",
  estimatedDelivery: "June 8th, 2025 - 5:00 PM",
  updates: [
    {
      timestamp: "June 6th, 10:00 AM",
      status: "Picked Up",
      location: "Washington, D.C.",
      photoUrl: "/placeholder.svg?width=100&height=100",
    },
    { timestamp: "June 7th, 02:00 PM", status: "In Transit", location: "Somewhere in Ohio" },
    { timestamp: "June 5th, 09:00 AM", status: "Shipment Created", location: "Sender's Home" },
  ],
}

export default function TrackDeliveryPage() {
  const [trackingId, setTrackingId] = useState("")
  const [shipment, setShipment] = useState<typeof sampleShipment | null>(null)

  const handleTrack = () => {
    // In a real app, fetch shipment data based on trackingId
    if (trackingId === "SUT12345") {
      setShipment(sampleShipment)
    } else {
      setShipment(null)
      alert("Invalid tracking ID. Use SUT12345 for demo.")
    }
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Track Your Delivery</h2>
      <Card>
        <CardHeader>
          <CardTitle>Enter Tracking ID</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-2">
          <Input placeholder="e.g., SUT12345" value={trackingId} onChange={(e) => setTrackingId(e.target.value)} />
          <Button onClick={handleTrack} className="bg-orange-600 hover:bg-orange-700">
            Track
          </Button>
        </CardContent>
      </Card>

      {shipment && (
        <Card>
          <CardHeader>
            <CardTitle>Shipment: {shipment.id}</CardTitle>
            <CardDescription>
              {shipment.item} from {shipment.origin} to {shipment.destination}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-muted rounded-md">
              <div>
                <p className="font-medium">{shipment.status}</p>
                <p className="text-sm text-muted-foreground">Est. Delivery: {shipment.estimatedDelivery}</p>
              </div>
              <Truck className="h-8 w-8 text-orange-600" />
            </div>

            <div className="flex items-center gap-3">
              <Image
                src={shipment.courier.profilePic || "/placeholder-user.jpg"}
                alt={shipment.courier.name}
                width={40}
                height={40}
                className="rounded-full"
              />
              <div>
                <p className="font-medium">Courier: {shipment.courier.name}</p>
                <Button variant="link" size="sm" className="p-0 h-auto text-orange-600">
                  <MessageSquare className="mr-1 h-3 w-3" /> Contact Courier
                </Button>
              </div>
            </div>

            <Separator />
            <h4 className="font-medium">Tracking History</h4>
            <ul className="space-y-4">
              {shipment.updates
                .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                .map((update, index) => (
                  <li key={index} className="flex gap-4">
                    <div className="flex flex-col items-center">
                      <div className={`p-2 rounded-full ${index === 0 ? "bg-orange-500 text-white" : "bg-muted"}`}>
                        {update.status.includes("Picked Up") ? (
                          <Package className="h-5 w-5" />
                        ) : update.status.includes("In Transit") ? (
                          <Truck className="h-5 w-5" />
                        ) : update.status.includes("Delivered") ? (
                          <Home className="h-5 w-5" />
                        ) : (
                          <Clock className="h-5 w-5" />
                        )}
                      </div>
                      {index < shipment.updates.length - 1 && <div className="w-0.5 flex-grow bg-border my-1"></div>}
                    </div>
                    <div>
                      <p className="font-medium">{update.status}</p>
                      <p className="text-sm text-muted-foreground">
                        {update.timestamp} - {update.location}
                      </p>
                      {update.photoUrl && (
                        <div className="mt-2">
                          <Image
                            src={update.photoUrl || "/placeholder.svg"}
                            alt={`Photo for ${update.status}`}
                            width={80}
                            height={80}
                            className="rounded-md border"
                          />
                          <p className="text-xs text-muted-foreground italic">
                            Photo proof at {update.status.toLowerCase()}
                          </p>
                        </div>
                      )}
                    </div>
                  </li>
                ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
