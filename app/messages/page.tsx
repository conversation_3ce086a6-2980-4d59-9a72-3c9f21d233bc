// Placeholder for messages page
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { MessageSquareText } from "lucide-react"

export default function MessagesPage() {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Messages</h2>
      <Card className="min-h-[50vh] flex flex-col items-center justify-center">
        <CardHeader>
          <CardTitle className="text-center">In-App Messaging</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <MessageSquareText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Your conversations with senders and couriers will appear here.</p>
          <p className="text-sm text-muted-foreground mt-2">(This feature is under development)</p>
        </CardContent>
      </Card>
    </div>
  )
}
