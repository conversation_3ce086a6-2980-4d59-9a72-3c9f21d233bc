"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Package, Truck, PlusCircle, History, Users } from "lucide-react"
import Link from "next/link"

// Sample data - replace with actual data fetching
const activeShipments = [
  {
    id: "1",
    item: "Injera Pack",
    destination: "Seattle, WA",
    status: "En Route",
    courier: "Abebe K.",
    eta: "Tomorrow, 3 PM",
  },
  {
    id: "2",
    item: "Coffee Beans",
    destination: "Atlanta, GA",
    status: "Awaiting Pickup",
    courier: "Hana G.",
    eta: "June 7th",
  },
]

const pastShipments = [
  { id: "3", item: "Berbere Spice", destination: "Minneapolis, MN", status: "Delivered", date: "May 20th" },
]

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Dashboard</h2>
        <Button asChild className="bg-orange-600 hover:bg-orange-700">
          <Link href="/create-shipment">
            <PlusCircle className="mr-2 h-4 w-4" /> New Shipment
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Shipments</CardTitle>
          <CardDescription>Track your ongoing deliveries.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {activeShipments.length > 0 ? (
            activeShipments.map((shipment) => (
              <div key={shipment.id} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">
                      {shipment.item} to {shipment.destination}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Courier: {shipment.courier} | ETA: {shipment.eta}
                    </p>
                  </div>
                  <span className="text-sm font-medium text-blue-600">{shipment.status}</span>
                </div>
                <Button variant="outline" size="sm" className="mt-2 w-full" asChild>
                  <Link href={`/track-delivery/${shipment.id}`}>Track Details</Link>
                </Button>
              </div>
            ))
          ) : (
            <p className="text-muted-foreground text-center py-4">No active shipments.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <Button variant="outline" className="flex-col h-24" asChild>
            <Link href="/create-shipment">
              <Package className="h-8 w-8 mb-1 text-orange-600" />
              Send Item
            </Link>
          </Button>
          <Button variant="outline" className="flex-col h-24" asChild>
            <Link href="/courier/post-route">
              <Truck className="h-8 w-8 mb-1 text-orange-600" />
              Offer Delivery
            </Link>
          </Button>
          <Button variant="outline" className="flex-col h-24" asChild>
            <Link href="/track-delivery">
              <History className="h-8 w-8 mb-1 text-orange-600" />
              Shipment History
            </Link>
          </Button>
          <Button variant="outline" className="flex-col h-24" asChild>
            <Link href="/find-courier">
              <Users className="h-8 w-8 mb-1 text-orange-600" />
              Find Couriers
            </Link>
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-green-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-green-700">Save with Sutway!</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-green-600">
            Did you know? Sending with Sutway is approximately <span className="font-bold">30% cheaper</span> than
            traditional services like FedEx!
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
