// Placeholder for verification status page
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import TrustBadge from "@/components/sutway/trust-badge"
import { ArrowLeft, CheckCircle, ShieldAlert, UploadCloud } from "lucide-react"
import Link from "next/link"

export default function VerificationPage() {
  const verificationStatus = {
    idVerified: true,
    churchMemberVerified: false,
    communityEndorsed: true,
    endorsements: 5,
  }

  return (
    <div className="space-y-6">
      <Button variant="outline" asChild className="mb-4">
        <Link href="/profile">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Profile
        </Link>
      </Button>
      <h2 className="text-2xl font-semibold">Account Verification</h2>
      <Card>
        <CardHeader>
          <CardTitle>Your Verification Status</CardTitle>
          <CardDescription>Building trust within the Sutway community.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div
            className={`flex items-center justify-between p-3 border rounded-md ${verificationStatus.idVerified ? "border-green-500 bg-green-50" : "border-yellow-500 bg-yellow-50"}`}
          >
            <div className="flex items-center">
              {verificationStatus.idVerified ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <ShieldAlert className="h-5 w-5 text-yellow-600 mr-2" />
              )}
              <span className="font-medium">Government ID Verified</span>
            </div>
            {verificationStatus.idVerified ? (
              <TrustBadge badgeName="ID Verified" />
            ) : (
              <Button size="sm" variant="outline">
                <UploadCloud className="mr-1 h-4 w-4" /> Upload ID
              </Button>
            )}
          </div>

          <div
            className={`flex items-center justify-between p-3 border rounded-md ${verificationStatus.churchMemberVerified ? "border-green-500 bg-green-50" : "border-yellow-500 bg-yellow-50"}`}
          >
            <div className="flex items-center">
              {verificationStatus.churchMemberVerified ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <ShieldAlert className="h-5 w-5 text-yellow-600 mr-2" />
              )}
              <span className="font-medium">Church Membership Verified</span>
            </div>
            {verificationStatus.churchMemberVerified ? (
              <TrustBadge badgeName="Church Member" />
            ) : (
              <Button size="sm" variant="outline">
                <UploadCloud className="mr-1 h-4 w-4" /> Submit Proof
              </Button>
            )}
          </div>

          <div
            className={`flex items-center justify-between p-3 border rounded-md ${verificationStatus.communityEndorsed ? "border-green-500 bg-green-50" : "border-gray-300"}`}
          >
            <div className="flex items-center">
              {verificationStatus.communityEndorsed ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <ShieldAlert className="h-5 w-5 text-gray-500 mr-2" />
              )}
              <span className="font-medium">Community Endorsements</span>
            </div>
            {verificationStatus.communityEndorsed ? (
              <TrustBadge badgeName={`Community Endorsed (${verificationStatus.endorsements})`} />
            ) : (
              <span className="text-sm text-muted-foreground">No endorsements yet</span>
            )}
          </div>
          <p className="text-xs text-muted-foreground mt-4">
            Verification helps build trust. The more verifications you complete, the more likely others are to transact
            with you. All submitted documents are handled securely.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
