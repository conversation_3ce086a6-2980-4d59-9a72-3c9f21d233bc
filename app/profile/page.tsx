"use client"

import { SelectItem } from "@/components/ui/select"

import { SelectContent } from "@/components/ui/select"

import { SelectValue } from "@/components/ui/select"

import { SelectTrigger } from "@/components/ui/select"

import { Select } from "@/components/ui/select"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import TrustBadge from "@/components/sutway/trust-badge"
import { Edit3, Shield, Star, Users, Bell, Globe, LogOut, Package, Truck } from "lucide-react"
import { useState } from "react"
import Link from "next/link"

// Sample user data
const userProfile = {
  name: "T<PERSON><PERSON><PERSON> Lemma",
  email: "<EMAIL>",
  phone: "+****************",
  memberSince: "January 2023",
  profilePic: "/placeholder-user.jpg",
  isCourier: true,
  language: "Amharic",
  elderMode: false,
  address: "123 Main St, Washington, D.C.",
  trustBadges: ["ID Verified", "Church Member", "Community Endorsement (5)"],
  senderRating: 4.9,
  courierRating: 4.7,
  culturalRespectScore: 5,
  completedShipments: 25,
  completedDeliveries: 18,
}

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState(userProfile)

  const handleInputChange = (field: string, value: any) => {
    setProfileData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    setIsEditing(false)
    // API call to save profileData
    console.log("Profile saved:", profileData)
    alert("Profile saved!")
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">My Profile</h2>
        <Button variant="outline" onClick={() => setIsEditing(!isEditing)}>
          <Edit3 className="mr-2 h-4 w-4" /> {isEditing ? "Cancel" : "Edit Profile"}
        </Button>
      </div>

      <Card>
        <CardHeader className="items-center text-center">
          <Avatar className="h-24 w-24 mb-2 border-2 border-orange-500">
            <AvatarImage src={profileData.profilePic || "/placeholder.svg"} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 1)}</AvatarFallback>
          </Avatar>
          <CardTitle className="text-2xl">{profileData.name}</CardTitle>
          <CardDescription>Member since {profileData.memberSince}</CardDescription>
          <div className="flex flex-wrap gap-2 justify-center mt-2">
            {profileData.trustBadges.map((badge) => (
              <TrustBadge key={badge} badgeName={badge} />
            ))}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {isEditing ? (
            <div className="space-y-3">
              <div>
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" value={profileData.name} onChange={(e) => handleInputChange("name", e.target.value)} />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={profileData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={profileData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <p>
                <strong>Email:</strong> {profileData.email}
              </p>
              <p>
                <strong>Phone:</strong> {profileData.phone}
              </p>
              <p>
                <strong>Address:</strong> {profileData.address}
              </p>
            </div>
          )}

          <Separator />

          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <p className="text-sm text-muted-foreground">Sender Rating</p>
              <p className="font-semibold text-lg flex items-center justify-center">
                <Star className="h-5 w-5 text-yellow-400 fill-yellow-400 mr-1" /> {profileData.senderRating.toFixed(1)}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Cultural Respect</p>
              <p className="font-semibold text-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-green-500 mr-1" /> {profileData.culturalRespectScore}/5
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Items Sent</p>
              <p className="font-semibold text-lg flex items-center justify-center">
                <Package className="h-5 w-5 text-orange-500 mr-1" /> {profileData.completedShipments}
              </p>
            </div>
            {profileData.isCourier && (
              <>
                <div>
                  <p className="text-sm text-muted-foreground">Courier Rating</p>
                  <p className="font-semibold text-lg flex items-center justify-center">
                    <Star className="h-5 w-5 text-yellow-400 fill-yellow-400 mr-1" />{" "}
                    {profileData.courierRating.toFixed(1)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Items Delivered</p>
                  <p className="font-semibold text-lg flex items-center justify-center">
                    <Truck className="h-5 w-5 text-blue-500 mr-1" /> {profileData.completedDeliveries}
                  </p>
                </div>
              </>
            )}
          </div>

          <Separator />
          <h3 className="font-medium text-lg">Settings</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="language" className="flex items-center">
                <Globe className="mr-2 h-4 w-4 text-muted-foreground" /> Preferred Language
              </Label>
              {isEditing ? (
                <Select value={profileData.language} onValueChange={(val) => handleInputChange("language", val)}>
                  <SelectTrigger id="language" className="w-[180px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="English">English</SelectItem>
                    <SelectItem value="Amharic">Amharic</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <span className="text-muted-foreground">{profileData.language}</span>
              )}
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="elderMode" className="flex items-center">
                <Users className="mr-2 h-4 w-4 text-muted-foreground" /> Elder Mode
              </Label>
              <Switch
                id="elderMode"
                checked={profileData.elderMode}
                onCheckedChange={(val) => handleInputChange("elderMode", val)}
                disabled={!isEditing}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="notifications" className="flex items-center">
                <Bell className="mr-2 h-4 w-4 text-muted-foreground" /> Notifications
              </Label>
              <Button variant="outline" size="sm" asChild>
                <Link href="/profile/notifications">Manage</Link>
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="verification" className="flex items-center">
                <Shield className="mr-2 h-4 w-4 text-muted-foreground" /> Account Verification
              </Label>
              <Button variant="outline" size="sm" asChild>
                <Link href="/profile/verification">View Status</Link>
              </Button>
            </div>
          </div>

          {isEditing && (
            <Button onClick={handleSave} className="w-full bg-orange-600 hover:bg-orange-700 mt-4">
              Save Changes
            </Button>
          )}

          {!isEditing && (
            <Button variant="outline" className="w-full mt-6">
              <LogOut className="mr-2 h-4 w-4" /> Log Out
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
