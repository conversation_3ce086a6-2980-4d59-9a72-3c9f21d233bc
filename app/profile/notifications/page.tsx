// Placeholder for notifications settings page
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function NotificationsSettingsPage() {
  return (
    <div className="space-y-6">
      <Button variant="outline" asChild className="mb-4">
        <Link href="/profile">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Profile
        </Link>
      </Button>
      <h2 className="text-2xl font-semibold">Notification Settings</h2>
      <Card>
        <CardHeader>
          <CardTitle>Manage Your Notifications</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-3 border rounded-md">
            <Label htmlFor="new-message" className="font-medium">
              New Messages
            </Label>
            <Switch id="new-message" defaultChecked />
          </div>
          <div className="flex items-center justify-between p-3 border rounded-md">
            <Label htmlFor="shipment-updates" className="font-medium">
              Shipment Updates
            </Label>
            <Switch id="shipment-updates" defaultChecked />
          </div>
          <div className="flex items-center justify-between p-3 border rounded-md">
            <Label htmlFor="courier-requests" className="font-medium">
              New Courier Requests (for Couriers)
            </Label>
            <Switch id="courier-requests" />
          </div>
          <div className="flex items-center justify-between p-3 border rounded-md">
            <Label htmlFor="promotions" className="font-medium">
              Promotions & Announcements
            </Label>
            <Switch id="promotions" />
          </div>
          <Button className="w-full mt-4 bg-orange-600 hover:bg-orange-700">Save Settings</Button>
        </CardContent>
      </Card>
    </div>
  )
}
