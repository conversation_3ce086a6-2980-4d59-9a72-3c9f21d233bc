import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Package, Truck } from "lucide-react"

export default function CtaSection() {
  return (
    <section className="py-16 md:py-24 bg-orange-600 text-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Experience Sutway?</h2>
        <p className="text-lg text-orange-100 mb-10 max-w-xl mx-auto">
          Join our growing community today. Send items with care, or help others while you travel.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" variant="secondary" className="bg-white text-orange-600 hover:bg-orange-50" asChild>
            <Link href="/create-shipment">
              Send a Package <Package className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <Button size="lg" variant="outline" className="border-white text-white hover:bg-orange-500" asChild>
            <Link href="/courier/post-route">
              Become a Courier <Truck className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
