import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { PackagePlus, UserSearch, Handshake, Truck, ListChecks, Coins } from "lucide-react"

const senderSteps = [
  {
    icon: <PackagePlus className="h-10 w-10 text-orange-600 mb-3" />,
    title: "Create Shipment",
    description: "Select item type (Injera, Coffee, etc.), pickup, and destination.",
  },
  {
    icon: <UserSearch className="h-10 w-10 text-orange-600 mb-3" />,
    title: "Find a Courier",
    description: "Browse trusted community members traveling your route.",
  },
  {
    icon: <Handshake className="h-10 w-10 text-orange-600 mb-3" />,
    title: "Connect & Send",
    description: "Arrange pickup and send your items with peace of mind.",
  },
]

const courierSteps = [
  {
    icon: <Truck className="h-10 w-10 text-blue-600 mb-3" />,
    title: "Post Your Route",
    description: "Share your upcoming travel plans between cities.",
  },
  {
    icon: <ListChecks className="h-10 w-10 text-blue-600 mb-3" />,
    title: "Accept Requests",
    description: "Review and accept delivery requests that fit your capacity.",
  },
  {
    icon: <Coins className="h-10 w-10 text-blue-600 mb-3" />,
    title: "Deliver & Earn",
    description: "Help your community and earn for your journey.",
  },
]

export default function HowItWorksSection() {
  return (
    <section className="py-16 md:py-24 bg-slate-50 dark:bg-slate-800">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-gray-900 dark:text-white">
          Simple & Secure Deliveries
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 text-center mb-12 max-w-2xl mx-auto">
          Whether you're sending cherished items or helping a neighbor, Sutway makes it easy.
        </p>

        <div className="grid md:grid-cols-2 gap-12">
          <div>
            <h3 className="text-2xl font-semibold mb-8 text-center text-orange-600">For Senders</h3>
            <div className="space-y-6">
              {senderSteps.map((step, index) => (
                <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex justify-center md:justify-start">{step.icon}</div>
                    <CardTitle className="text-xl text-center md:text-left">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300 text-center md:text-left">{step.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-2xl font-semibold mb-8 text-center text-blue-600">For Couriers</h3>
            <div className="space-y-6">
              {courierSteps.map((step, index) => (
                <Card key={index} className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex justify-center md:justify-start">{step.icon}</div>
                    <CardTitle className="text-xl text-center md:text-left">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300 text-center md:text-left">{step.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
