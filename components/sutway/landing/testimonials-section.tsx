import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    location: "Sender, Washington D.C.",
    image: "/placeholder-user.jpg", // Replace with actual user image
    testimonial:
      "<PERSON><PERSON><PERSON> made it so easy to send fresh Injera to my son in Atlanta! The courier was so respectful and communicative. Much cheaper than FedEx too!",
    rating: 5,
  },
  {
    name: "Bereket T.",
    location: "Courier, Minneapolis",
    image: "/placeholder-user.jpg", // Replace with actual user image
    testimonial:
      "I travel often for work. <PERSON><PERSON><PERSON> helps me make extra money and connect with fellow Ethiopians. It feels good to help out.",
    rating: 5,
  },
  {
    name: "Fatuma A.",
    location: "Sender, Seattle",
    image: "/placeholder-user.jpg", // Replace with actual user image
    testimonial:
      "I was worried about sending delicate religious items, but the 'Cultural Respect' score gave me confidence. Everything arrived perfectly.",
    rating: 5,
  },
]

export default function TestimonialsSection() {
  return (
    <section className="py-16 md:py-24 bg-slate-50 dark:bg-slate-800">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900 dark:text-white">
          Hear From Our <span className="text-orange-600">Community</span>
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="shadow-lg flex flex-col">
              <CardContent className="p-6 flex-grow">
                <div className="flex items-center mb-4">
                  <Avatar className="h-12 w-12 mr-4">
                    <AvatarImage src={testimonial.image || "/placeholder.svg"} alt={testimonial.name} />
                    <AvatarFallback>{testimonial.name.substring(0, 1)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">{testimonial.name}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{testimonial.location}</p>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 mb-4 flex-grow">"{testimonial.testimonial}"</p>
              </CardContent>
              <div className="p-6 border-t dark:border-gray-700 mt-auto">
                <div className="flex">
                  {Array(testimonial.rating)
                    .fill(0)
                    .map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                    ))}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
