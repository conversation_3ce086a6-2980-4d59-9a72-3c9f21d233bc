// Re-providing LandingHeader to ensure it's available for the LandingLayout
"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Globe } from "lucide-react"
import { useState } from "react"

export default function LandingHeader() {
  const [language, setLanguage] = useState("EN")

  const toggleLanguage = () => {
    setLanguage(language === "EN" ? "AM" : "EN")
    alert(`Language switched to ${language === "EN" ? "Amharic" : "English"} (demo).`)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        <Link href="/landing" className="mr-auto">
          <h1 className="text-3xl font-bold text-orange-600">Sutway</h1>
        </Link>
        <nav className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={toggleLanguage} aria-label="Toggle language">
            <Globe className="h-4 w-4 mr-1" /> {language}
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard">Log In</Link> {/* This should link to your app's login/dashboard */}
          </Button>
          <Button size="sm" className="bg-orange-600 hover:bg-orange-700" asChild>
            <Link href="/create-shipment">Sign Up</Link> {/* This should link to your app's sign-up or main app area */}
          </Button>
        </nav>
      </div>
    </header>
  )
}
