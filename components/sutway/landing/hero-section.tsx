"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>R<PERSON>, Truck } from "lucide-react"

export default function HeroSection() {
  return (
    <section className="relative bg-gradient-to-b from-orange-50 via-amber-50 to-background py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          <div className="text-center md:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-gray-900 dark:text-white">
              Ship with <span className="text-orange-600">Trust</span>, Connect with{" "}
              <span className="text-orange-600">Community</span>.
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300">
              Sutway is your peer-to-peer delivery platform for the Ethiopian-American community. Send Injera, coffee,
              spices, and more, reliably and affordably, with people you trust.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
              <Button size="lg" className="bg-orange-600 hover:bg-orange-700" asChild>
                <Link href="/create-shipment">
                  Send a Package <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-orange-600 text-orange-600 hover:bg-orange-50"
                asChild
              >
                <Link href="/courier/post-route">
                  Become a Courier <Truck className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
            <p className="mt-6 text-sm text-gray-500 dark:text-gray-400">
              Save up to <span className="font-semibold text-green-600">30%</span> compared to traditional shipping!
            </p>
          </div>
          <div className="relative h-64 md:h-auto mt-8 md:mt-0">
            <Image
              src="/placeholder.svg?width=600&height=450" // Replace with a culturally relevant image
              alt="Community members connecting over a delivery"
              width={600}
              height={450}
              className="rounded-lg shadow-xl object-cover mx-auto"
              priority
            />
            {/* Example of a subtle cultural pattern overlay or background element */}
            <div className="absolute inset-0 bg-gradient-to-tr from-orange-500/10 via-transparent to-blue-500/10 rounded-lg -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
