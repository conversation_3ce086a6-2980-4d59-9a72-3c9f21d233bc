import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { HeartHandshake, ShieldCheck, Home, Users } from "lucide-react"

export default function CommunityFocusSection() {
  return (
    <section className="py-16 md:py-24 bg-orange-50 dark:bg-orange-900/30">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <Image
              src="/placeholder.svg?width=500&height=400" // Replace with an image of community gathering or Ethiopian cultural elements
              alt="Ethiopian community gathering"
              width={500}
              height={400}
              className="rounded-lg shadow-xl object-cover mx-auto"
            />
          </div>
          <div className="text-center md:text-left">
            <HeartHandshake className="h-12 w-12 text-orange-600 mb-4 mx-auto md:mx-0" />
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900 dark:text-white">
              Connecting Our Community, One Delivery at a Time
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
              Sutway is more than just a delivery app. It's a platform built on shared values, trust, and the desire to
              support one another. We facilitate connections that strengthen our Ethiopian-American community ties
              across cities.
            </p>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400 mb-8 text-left">
              <li className="flex items-center">
                <ShieldCheck className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                <span>Fostering trust through shared cultural understanding.</span>
              </li>
              <li className="flex items-center">
                <Home className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
                <span>Convenient pickups at familiar community spots like churches and markets.</span>
              </li>
              <li className="flex items-center">
                <Users className="h-5 w-5 text-purple-500 mr-2 flex-shrink-0" />
                <span>Supporting elders with an easy-to-use interface.</span>
              </li>
            </ul>
            <Button size="lg" className="bg-orange-600 hover:bg-orange-700" asChild>
              <Link href="/create-shipment">Join the Sutway Community</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
