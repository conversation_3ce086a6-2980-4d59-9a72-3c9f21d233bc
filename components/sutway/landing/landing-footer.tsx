import Link from "next/link"
import { Facebook, Twitter, Instagram, Linkedin } from "lucide-react"

export default function LandingFooter() {
  const currentYear = new Date().getFullYear()
  return (
    <footer className="bg-gray-100 dark:bg-gray-800 border-t dark:border-gray-700">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="text-xl font-bold text-orange-600 mb-4">Sutway</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Connecting communities, one delivery at a time. Built for the Ethiopian-American community with trust and
              care.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/landing#how-it-works"
                  className="text-gray-600 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
                >
                  How It Works
                </Link>
              </li>
              <li>
                <Link
                  href="/landing#features"
                  className="text-gray-600 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
                >
                  Features
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">Support</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/faq"
                  className="text-gray-600 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
                >
                  FAQ
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-600 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
                >
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">Connect With Us</h4>
            <div className="flex space-x-4">
              <Link
                href="#"
                aria-label="Facebook"
                className="text-gray-500 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
              >
                <Facebook className="h-6 w-6" />
              </Link>
              <Link
                href="#"
                aria-label="Twitter"
                className="text-gray-500 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
              >
                <Twitter className="h-6 w-6" />
              </Link>
              <Link
                href="#"
                aria-label="Instagram"
                className="text-gray-500 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
              >
                <Instagram className="h-6 w-6" />
              </Link>
              <Link
                href="#"
                aria-label="LinkedIn"
                className="text-gray-500 hover:text-orange-600 dark:text-gray-400 dark:hover:text-orange-500"
              >
                <Linkedin className="h-6 w-6" />
              </Link>
            </div>
          </div>
        </div>
        <div className="text-center text-sm text-gray-500 dark:text-gray-400 border-t dark:border-gray-700 pt-8">
          &copy; {currentYear} Sutway. All rights reserved. Made with ❤️ for our community.
        </div>
      </div>
    </footer>
  )
}
