import { ShieldCheck, Users, Globe, Coffee, Percent, MessageCircle, UserCheck, Home } from "lucide-react"

const features = [
  {
    icon: <Coffee className="h-8 w-8 text-orange-600" />,
    title: "Culturally Specific Items",
    description: "Easily send Injera, Be<PERSON>e, traditional clothing, religious items, and more.",
  },
  {
    icon: <ShieldCheck className="h-8 w-8 text-green-600" />,
    title: "Trust & Verification",
    description: "ID verification, church member badges, and community endorsements build trust.",
  },
  {
    icon: <Users className="h-8 w-8 text-blue-600" />,
    title: "Dual Rating System",
    description: "Rate couriers on reliability and unique 'Cultural Respect' score.",
  },
  {
    icon: <Percent className="h-8 w-8 text-teal-600" />,
    title: "Cost Savings",
    description: "Up to 30% cheaper than traditional shipping methods like FedEx.",
  },
  {
    icon: <Globe className="h-8 w-8 text-purple-600" />,
    title: "Bilingual Interface",
    description: "Toggle between English and Amharic for ease of use.",
  },
  {
    icon: <Home className="h-8 w-8 text-yellow-600" />,
    title: "Community Hubs",
    description: "Preset pickup/delivery at Ethiopian churches and businesses.",
  },
  {
    icon: <MessageCircle className="h-8 w-8 text-pink-600" />,
    title: "In-App Messaging",
    description: "Communicate directly and securely with senders/couriers.",
  },
  {
    icon: <UserCheck className="h-8 w-8 text-indigo-600" />,
    title: "Elder-Friendly Mode",
    description: "Simplified UI and larger fonts for accessibility.",
  },
]

export default function FeaturesSection() {
  return (
    <section className="py-16 md:py-24 bg-background dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-gray-900 dark:text-white">
          Why Choose <span className="text-orange-600">Sutway</span>?
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 text-center mb-12 max-w-2xl mx-auto">
          Built by the community, for the community. We understand your needs.
        </p>
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="p-6 bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{feature.title}</h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
