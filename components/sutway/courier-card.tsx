"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ShieldCheck, Star, Users, CalendarDays, MessageSquare, CheckCircle } from "lucide-react"
import TrustBadge from "./trust-badge" // We'll create this next
import { cn } from "@/lib/utils"

type Courier = {
  id: string
  name: string
  profilePic?: string
  rating: number
  culturalRespect: number
  deliveries: number
  memberSince: string
  verifiedBadges: string[]
  route?: string
  eta?: string
  price?: number
}

interface CourierCardProps {
  courier: Courier
  isSelected?: boolean
  onSelect?: (id: string) => void
  showSelectButton?: boolean
  showContactButton?: boolean
}

export default function CourierCard({
  courier,
  isSelected,
  onSelect,
  showSelectButton = true,
  showContactButton = false,
}: CourierCardProps) {
  return (
    <Card className={cn("overflow-hidden", isSelected && "border-2 border-orange-500 ring-2 ring-orange-500")}>
      <CardHeader className="flex flex-row items-start gap-4 p-4 bg-muted/30">
        <Avatar className="h-16 w-16 border">
          <AvatarImage src={courier.profilePic || "/placeholder-user.jpg"} alt={courier.name} />
          <AvatarFallback>{courier.name.substring(0, 1)}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <CardTitle className="text-xl">{courier.name}</CardTitle>
          <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" /> {courier.rating.toFixed(1)}
            </div>
            <div className="flex items-center" title="Cultural Respect Score">
              <Users className="h-4 w-4 text-green-600 mr-1" /> {courier.culturalRespect}/5
            </div>
          </div>
          <div className="flex flex-wrap gap-1 mt-2">
            {courier.verifiedBadges.map((badge) => (
              <TrustBadge key={badge} badgeName={badge} />
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 space-y-2">
        {courier.route && (
          <p className="text-sm">
            <span className="font-medium">Route:</span> {courier.route}
          </p>
        )}
        {courier.eta && (
          <p className="text-sm">
            <span className="font-medium">ETA:</span> {courier.eta}
          </p>
        )}
        {courier.price !== undefined && (
          <p className="text-lg font-semibold text-orange-600">${courier.price.toFixed(2)}</p>
        )}

        <div className="text-xs text-muted-foreground flex items-center gap-4">
          <span>
            <ShieldCheck className="inline h-3 w-3 mr-1" />
            {courier.deliveries} deliveries
          </span>
          <span>
            <CalendarDays className="inline h-3 w-3 mr-1" />
            Joined {courier.memberSince}
          </span>
        </div>
      </CardContent>
      {(showSelectButton || showContactButton) && (
        <CardFooter className="p-4 flex gap-2">
          {showSelectButton && onSelect && (
            <Button
              variant={isSelected ? "default" : "outline"}
              className={cn("w-full", isSelected && "bg-orange-600 hover:bg-orange-700")}
              onClick={() => onSelect(courier.id)}
            >
              {isSelected && <CheckCircle className="mr-2 h-4 w-4" />}
              {isSelected ? "Selected" : "Select Courier"}
            </Button>
          )}
          {showContactButton && (
            <Button variant="outline" className="w-full">
              <MessageSquare className="mr-2 h-4 w-4" /> Message
            </Button>
          )}
        </CardFooter>
      )}
    </Card>
  )
}
