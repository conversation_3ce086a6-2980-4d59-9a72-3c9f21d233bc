"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, PlusCircle, Search, MapPin, User } from "lucide-react"
import { cn } from "@/lib/utils"

const navItems = [
  { href: "/dashboard", label: "Dashboard", icon: Home },
  { href: "/create-shipment", label: "New", icon: PlusCircle },
  { href: "/find-courier", label: "Find", icon: Search },
  { href: "/track-delivery", label: "Track", icon: MapPin },
  { href: "/profile", label: "Profile", icon: User },
]

export default function BottomNav() {
  const pathname = usePathname()

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-background border-t sm:hidden z-50">
      <div className="flex justify-around items-center h-16">
        {navItems.map((item) => {
          const isActive = pathname === item.href || (item.href !== "/dashboard" && pathname.startsWith(item.href))
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center text-xs p-2 rounded-md",
                isActive ? "text-orange-600" : "text-muted-foreground hover:text-orange-500",
              )}
            >
              <item.icon className={cn("h-6 w-6 mb-0.5", isActive ? "fill-current" : "")} />
              {item.label}
            </Link>
          )
        })}
      </div>
    </nav>
  )
}
