import { Badge } from "@/components/ui/badge"
import { Shield<PERSON>heck, CheckSquare, Users } from "lucide-react" // Using CheckSquare for Church Member

interface TrustBadgeProps {
  badgeName: string
}

export default function TrustBadge({ badgeName }: TrustBadgeProps) {
  let icon = <ShieldCheck className="mr-1 h-3 w-3" />
  let variant: "default" | "secondary" | "destructive" | "outline" = "secondary"
  let textClass = "text-xs"

  if (badgeName.toLowerCase().includes("id verified")) {
    variant = "default" // More prominent
    icon = <ShieldCheck className="mr-1 h-3 w-3 text-green-500" />
    textClass = "text-xs text-green-700 bg-green-100"
  } else if (badgeName.toLowerCase().includes("church member")) {
    icon = <Users className="mr-1 h-3 w-3 text-blue-500" /> // Using Users icon for community aspect
    textClass = "text-xs text-blue-700 bg-blue-100"
  } else if (badgeName.toLowerCase().includes("community endorsement")) {
    icon = <CheckSquare className="mr-1 h-3 w-3 text-purple-500" />
    textClass = "text-xs text-purple-700 bg-purple-100"
  }

  return (
    <Badge variant="outline" className={`py-0.5 px-1.5 ${textClass} border`}>
      {icon}
      {badgeName}
    </Badge>
  )
}
