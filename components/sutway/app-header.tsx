"use client"

import Link from "next/link"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Globe, MessageSquare, Settings, UserCircle } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"

export default function AppHeader() {
  const [language, setLanguage] = useState("EN")
  const [isElderMode, setIsElderMode] = useState(false)

  const toggleLanguage = () => {
    setLanguage(language === "EN" ? "AM" : "EN")
    // Add actual language switching logic here
  }

  const toggleElderMode = () => {
    setIsElderMode(!isElderMode)
    // Add actual elder mode styling changes here (e.g., larger fonts, simplified layout)
    if (!isElderMode) {
      document.documentElement.style.setProperty("--font-scale-factor", "1.25")
    } else {
      document.documentElement.style.removeProperty("--font-scale-factor")
    }
    alert(`Elder mode ${!isElderMode ? "enabled" : "disabled"}. UI will adjust accordingly.`)
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <Link href="/dashboard" className="mr-auto">
          <h1 className="text-2xl font-bold text-orange-600">Sutway</h1>
        </Link>
        <nav className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={toggleLanguage} aria-label="Toggle language">
            <Globe className="h-5 w-5" />
            <span className="sr-only">Toggle Language ({language})</span>
          </Button>
          <Button variant="ghost" size="icon" asChild aria-label="Messages">
            <Link href="/messages">
              <MessageSquare className="h-5 w-5" />
              <span className="sr-only">Messages</span>
            </Link>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" aria-label="User Menu">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder-user.jpg" alt="User" />
                  <AvatarFallback>U</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile">
                  <UserCircle className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={toggleElderMode}>
                <Settings className="mr-2 h-4 w-4" />
                <span>{isElderMode ? "Disable" : "Enable"} Elder Mode</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Log out</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </nav>
      </div>
    </header>
  )
}
